//  StoreMob
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woosignal/models/response/woosignal_app.dart';

class NoConnectionPage extends NyStatefulWidget {
  static RouteView path = ("/no-connection", (_) => NoConnectionPage());

  NoConnectionPage({super.key}) : super(child: () => _NoConnectionPageState());
}

class _NoConnectionPageState extends NyPage<NoConnectionPage> {
  _NoConnectionPageS<PERSON>();

  @override
  get init => () {
        if (getEnv('APP_DEBUG') == true) {
          NyLogger.error('WooCommerce site is not connected');
        }
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: SafeAreaWidget(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Icon(
                Icons.error_outline,
                size: 100,
                color: Colors.black54,
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  trans("Oops, something went wrong"),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              LinkButton(title: trans("Retry"), action: _retry),
            ],
          ),
        ),
      ),
    );
  }

  _retry() async {
    WooSignalApp? wooSignalApp = await (appWooSignal((api) => api.getApp()));

    if (wooSignalApp == null) {
      showToast(
          title: trans("Oops"),
          description: trans("Retry later"),
          icon: Icons.info_outline,
          style: ToastNotificationStyleType.info);
      return;
    }

    AppHelper.instance.appConfig = wooSignalApp;
    routeToInitial();
  }
}
