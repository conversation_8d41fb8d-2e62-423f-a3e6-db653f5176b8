//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/checkout_status_page.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/data/order_wc.dart';
import '/resources/pages/checkout_confirmation_page.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '../../services/woocommerce_service.dart';


stripePay(context) async {
  bool liveMode = getEnv('STRIPE_LIVE_MODE', defaultValue: false);

  // CONFIGURE STRIPE
  Stripe.stripeAccountId = getEnv('STRIPE_ACCOUNT');

  Stripe.publishableKey = liveMode
      ? "pk_live_IyS4Vt86L49jITSfaUShumzi"
      : "pk_test_0jMmpBntJ6UkizPkfiB8ZJxH"; // Don't change this value
  await Stripe.instance.applySettings();

  if (Stripe.stripeAccountId == '') {
    NyLogger.error(
        'You need to connect your Stripe account to WooSignal via the dashboard https://woosignal.com/dashboard');
    return;
  }

  try {
    Map<String, dynamic>? rsp = {};
    //   // CHECKOUT HELPER
    await checkout((total, billingDetails, cart) async {
      String cartShortDesc = await cart.cartShortDesc();

      // TODO: Implement Stripe payment intent with WooCommerce API
      rsp = {}; // Placeholder - implement Stripe integration
    });

    if (rsp == null) {
      showToastNotification(context,
          title: trans("Oops!"),
          description: trans("Something went wrong, please try again."),
          icon: Icons.payment,
          style: ToastNotificationStyleType.warning);
      updateState(CheckoutConfirmationPage.path.nyPageName(),
          data: {"reloadState": false});
      return;
    }

    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
          style: Theme.of(context).brightness == Brightness.light
              ? ThemeMode.light
              : ThemeMode.dark,
          merchantDisplayName: getEnv('APP_NAME', defaultValue: 'LabelStoreMax'),
          customerId: rsp!['customer'],
          paymentIntentClientSecret: rsp!['client_secret'],
          customerEphemeralKeySecret: rsp!['ephemeral_key'],
          setupIntentClientSecret: rsp!['setup_intent_secret']),
    );

    await Stripe.instance.presentPaymentSheet();

    PaymentIntent paymentIntent =
        await Stripe.instance.retrievePaymentIntent(rsp!['client_secret']);

    if (paymentIntent.status == PaymentIntentsStatus.Unknown) {
      showToastNotification(
        context,
        title: trans("Oops!"),
        description: trans("Something went wrong, please try again."),
        icon: Icons.payment,
        style: ToastNotificationStyleType.warning,
      );
    }

    if (paymentIntent.status != PaymentIntentsStatus.Succeeded) {
      return;
    }

    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": true});

    WooOrder orderWC = await buildOrderWC(markPaid: true);
    WooOrder? order = await WooCommerceService().createOrder(orderWC);

    if (order.id == null) {
      showToastNotification(
        context,
        title: trans("Error"),
        description: trans("Something went wrong, please contact our store"),
      );
      updateState(CheckoutConfirmationPage.path.nyPageName(),
          data: {"reloadState": false});
      return;
    }

    routeTo(CheckoutStatusPage.path,
        navigationType: NavigationType.pushAndForgetAll, data: order);
  } on StripeException catch (e) {
    if (getEnv('APP_DEBUG', defaultValue: true)) {
      NyLogger.error(e.error.message!);
    }
    showToastNotification(
      context,
      title: trans("Oops!"),
      description: e.error.localizedMessage!,
      icon: Icons.payment,
      style: ToastNotificationStyleType.warning,
    );
    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": false});
  } catch (ex) {
    if (getEnv('APP_DEBUG', defaultValue: true)) {
      NyLogger.error(ex.toString());
    }
    showToastNotification(
      context,
      title: trans("Oops!"),
      description: trans("Something went wrong, please try again."),
      icon: Icons.payment,
      style: ToastNotificationStyleType.warning,
    );
    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": false});
  }
}
