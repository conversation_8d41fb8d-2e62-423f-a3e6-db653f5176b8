//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'dart:io';

import '/app/models/billing_details.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart';


Future<WooOrder> buildOrderWC({bool markPaid = true}) async {
  CheckoutSession checkoutSession = CheckoutSession.getInstance;
  WooOrder orderWC = WooOrder();

  String paymentMethodName = checkoutSession.paymentType!.name;

  orderWC.paymentMethod = Platform.isAndroid
      ? "$paymentMethodName - Android App"
      : "$paymentMethodName - IOS App";

  orderWC.paymentMethodTitle = paymentMethodName.toLowerCase();

  orderWC.setPaid = markPaid;
  orderWC.status = "pending";
  orderWC.currency = "LYD"; // TODO: Get from app config
  // TODO: Add customer ID when user authentication is implemented

  List<Map<String, dynamic>> lineItems = [];
  List<CartLineItem> cartItems = await Cart.getInstance.getCart();
  for (var cartItem in cartItems) {
    Map<String, dynamic> tmpLineItem = {
      'quantity': cartItem.quantity,
      'name': cartItem.name,
      'product_id': cartItem.productId,
    };

    if (cartItem.variationId != null && cartItem.variationId != 0) {
      tmpLineItem['variation_id'] = cartItem.variationId;
    }

    tmpLineItem['subtotal'] = ((double.tryParse(cartItem.subtotal) ?? 0.0) *
            (double.tryParse(cartItem.quantity.toString()) ?? 0.0))
        .toString();
    lineItems.add(tmpLineItem);
  }

  orderWC.lineItems = lineItems;

  BillingDetails billingDetails = checkoutSession.billingDetails!;

  // TODO: Set billing details using proper WooCommerce API types
  // For now, create a basic order without detailed billing/shipping

  // TODO: Add shipping details when WooCommerce API types are properly configured
  // TODO: Add shipping lines when shipping is configured
  // TODO: Add fee lines for taxes when tax system is configured
  // TODO: Add coupon lines when coupon system is configured

  if (checkoutSession.customerNote?.isNotEmpty ?? false) {
    orderWC.customerNote = checkoutSession.customerNote;
  }

  return orderWC;
}
