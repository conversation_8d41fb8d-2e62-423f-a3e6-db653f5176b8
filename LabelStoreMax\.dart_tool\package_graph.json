{"roots": ["flutter_app"], "packages": [{"name": "flutter_app", "version": "1.0.0+1", "dependencies": ["analyzer", "animate_do", "auto_size_text", "bubble_tab_indicator", "cached_network_image", "collection", "cupertino_icons", "firebase_core", "firebase_messaging", "flutter", "flutter_inappwebview", "flutter_localizations", "flutter_rating_bar", "flutter_spinkit", "flutter_staggered_grid_view", "flutter_stripe", "flutter_swiper_view", "flutter_widget_from_html_core", "google_fonts", "html", "intl", "math_expressions", "nylo_framework", "package_info_plus", "pretty_dio_logger", "pull_to_refresh_flutter3", "razorpay_flutter", "url_launcher", "validated", "woosignal", "wp_json_api"], "devDependencies": ["flutter_launcher_icons", "flutter_lints", "flutter_test", "lints"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "flutter_launcher_icons", "version": "0.14.3", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "animate_do", "version": "4.2.0", "dependencies": ["flutter"]}, {"name": "pretty_dio_logger", "version": "1.4.0", "dependencies": ["dio"]}, {"name": "razorpay_flutter", "version": "1.4.0", "dependencies": ["eventify", "flutter", "fluttertoast"]}, {"name": "flutter_stripe", "version": "11.5.0", "dependencies": ["flutter", "meta", "stripe_android", "stripe_ios", "stripe_platform_interface"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "firebase_core", "version": "3.13.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_messaging", "version": "15.2.6", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "flutter_swiper_view", "version": "1.1.8", "dependencies": ["flutter"]}, {"name": "flutter_staggered_grid_view", "version": "0.7.0", "dependencies": ["flutter"]}, {"name": "flutter_rating_bar", "version": "4.0.1", "dependencies": ["flutter"]}, {"name": "flutter_widget_from_html_core", "version": "0.16.0", "dependencies": ["csslib", "flutter", "html", "logging"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "auto_size_text", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "flutter_spinkit", "version": "5.2.1", "dependencies": ["flutter"]}, {"name": "validated", "version": "2.0.0", "dependencies": []}, {"name": "math_expressions", "version": "2.7.0", "dependencies": ["petitparser", "vector_math"]}, {"name": "bubble_tab_indicator", "version": "0.1.6", "dependencies": ["flutter"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "pull_to_refresh_flutter3", "version": "2.0.2", "dependencies": ["flutter"]}, {"name": "flutter_inappwebview", "version": "6.1.5", "dependencies": ["flutter", "flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_platform_interface", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "wp_json_api", "version": "4.3.3", "dependencies": ["collection", "dio", "flutter", "flutter_secure_storage", "nylo_support"]}, {"name": "woosignal", "version": "4.2.0", "dependencies": ["device_meta", "dio", "encrypt", "flutter", "intl"]}, {"name": "nylo_framework", "version": "6.8.7", "dependencies": ["args", "collection", "date_field", "dio", "error_stack", "flutter", "flutter_dotenv", "flutter_secure_storage", "nylo_support", "recase", "skeletonizer", "theme_provider"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "analyzer", "version": "7.4.5", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "fluttertoast", "version": "8.2.12", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "eventify", "version": "1.0.1", "dependencies": []}, {"name": "stripe_platform_interface", "version": "11.5.0", "dependencies": ["flutter", "freezed_annotation", "json_annotation", "meta", "plugin_platform_interface"]}, {"name": "stripe_ios", "version": "11.5.0", "dependencies": ["flutter"]}, {"name": "stripe_android", "version": "11.5.0", "dependencies": ["flutter"]}, {"name": "firebase_core_web", "version": "2.23.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_messaging_web", "version": "3.10.6", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.6", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "flutter_inappwebview_windows", "version": "0.6.0", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_web", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_inappwebview_macos", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_ios", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_android", "version": "1.1.3", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_platform_interface", "version": "1.3.0+1", "dependencies": ["flutter", "flutter_inappwebview_internal_annotations", "plugin_platform_interface"]}, {"name": "win32", "version": "5.13.0", "dependencies": ["ffi"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "nylo_support", "version": "6.28.5", "dependencies": ["animate_do", "app_badge_plus", "characters", "date_field", "dio", "equatable", "error_stack", "ffi", "flutter", "flutter_dotenv", "flutter_local_notifications", "flutter_localizations", "flutter_multi_formatter", "flutter_secure_storage", "flutter_staggered_grid_view", "flutter_styled_toast", "flutter_timezone", "flutter_web_plugins", "get_time_ago", "intl", "mask_text_input_formatter", "path_provider", "pretty_dio_logger", "pull_to_refresh_flutter3", "recase", "rxdart", "skeletonizer", "theme_provider", "timezone", "uuid", "validated", "win32"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "encrypt", "version": "5.0.3", "dependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"]}, {"name": "device_meta", "version": "2.1.7", "dependencies": ["device_info_plus", "flutter", "nylo_support", "universal_platform", "uuid"]}, {"name": "date_field", "version": "6.0.3+1", "dependencies": ["flutter", "intl"]}, {"name": "error_stack", "version": "1.10.3", "dependencies": ["flutter", "flutter_secure_storage", "url_launcher"]}, {"name": "recase", "version": "4.1.0", "dependencies": []}, {"name": "skeletonizer", "version": "2.0.1", "dependencies": ["flutter"]}, {"name": "theme_provider", "version": "0.6.0", "dependencies": ["flutter", "shared_preferences"]}, {"name": "flutter_dotenv", "version": "5.2.1", "dependencies": ["flutter"]}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "82.0.0", "dependencies": ["meta"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "_flutterfire_internals", "version": "1.3.55", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "flutter_inappwebview_internal_annotations", "version": "1.2.0", "dependencies": []}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "mask_text_input_formatter", "version": "2.9.0", "dependencies": ["flutter"]}, {"name": "app_badge_plus", "version": "1.2.3", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_timezone", "version": "4.1.1", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications", "version": "19.2.1", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "flutter_local_notifications_windows", "timezone"]}, {"name": "flutter_multi_formatter", "version": "2.13.7", "dependencies": ["base58check", "bech32", "collection", "flutter"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "flutter_styled_toast", "version": "2.2.1", "dependencies": ["flutter", "flutter_localizations"]}, {"name": "get_time_ago", "version": "2.3.1", "dependencies": ["intl"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "asn1lib", "version": "1.6.4", "dependencies": []}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "device_info_plus", "version": "11.4.0", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "flutter_local_notifications_platform_interface", "version": "9.0.0", "dependencies": ["plugin_platform_interface"]}, {"name": "flutter_local_notifications_windows", "version": "1.0.0", "dependencies": ["ffi", "flutter", "flutter_local_notifications_platform_interface", "meta", "timezone", "xml"]}, {"name": "flutter_local_notifications_linux", "version": "6.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "bech32", "version": "0.2.2", "dependencies": ["convert"]}, {"name": "base58check", "version": "2.0.0", "dependencies": ["collection", "crypto"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "win32_registry", "version": "2.1.0", "dependencies": ["ffi", "meta", "win32"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}], "configVersion": 1}