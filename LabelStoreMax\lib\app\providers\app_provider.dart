import 'package:flutter/services.dart';
import '/config/storage_keys.dart';
import '/bootstrap/app_helper.dart';
import '/config/decoders.dart';
import '/config/design.dart';
import '/config/theme.dart';
import '/config/validation_rules.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/config/localization.dart';
import '/app/models/app_config.dart';

class AppProvider implements NyProvider {
  @override
  boot(Nylo nylo) async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // Initialize basic app configuration
    AppHelper.instance.appConfig = AppConfig.defaultConfig();

    // Set locale
    Locale locale = Locale(getEnv('DEFAULT_LOCALE', defaultValue: 'en'));

    /// NyLocalization
    await NyLocalization.instance.init(
      localeType: localeType,
      languageCode: locale.languageCode,
      assetsDirectory: assetsDirectory,
    );

    nylo.addLoader(loader);
    nylo.addLogo(logo);
    nylo.addThemes(appThemes);
    nylo.addToastNotification(getToastNotificationWidget);
    nylo.addValidationRules(validationRules);
    nylo.addModelDecoders(modelDecoders);
    nylo.addControllers(controllers);
    nylo.addApiDecoders(apiDecoders);
    nylo.useErrorStack();

    if (nylo.getAuthKey() == null) {
      nylo.addAuthKey(Keys.auth);
    }
    await nylo.syncKeys(Keys.syncedOnBoot);

    return nylo;
  }

  @override
  afterBoot(Nylo nylo) async {}
}
