//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/resources/pages/checkout_status_page.dart';
import '/app/models/cart.dart';
import '/bootstrap/data/order_wc.dart';
import '/resources/pages/checkout_confirmation_page.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '../../services/woocommerce_service.dart';


razorPay(context) async {
  Razorpay razorpay = Razorpay();

  razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS,
      (PaymentSuccessResponse response) async {
    WooOrder orderWC = await buildOrderWC(markPaid: true);

    WooOrder? order = await WooCommerceService().createOrder(orderWC);

    if (order.id == null) {
      showToastNotification(
        context,
        title: "Error".tr(),
        description: trans("Something went wrong, please contact our store"),
      );
      updateState(CheckoutConfirmationPage.path.nyPageName(),
          data: {"reloadState": false});
      return;
    }
    Cart.getInstance.clear();
    routeTo(CheckoutStatusPage.path, data: order);
  });

  razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, (PaymentFailureResponse response) {
    showToastNotification(context,
        title: trans("Error"),
        description: response.message ?? "",
        style: ToastNotificationStyleType.warning);
    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": false});
  });

  razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);

  // CHECKOUT HELPER
  await checkout((total, billingDetails, cart) async {
    var options = {
      'key': getEnv('RAZORPAY_API_KEY'),
      'amount': (double.parse(total) * 100).toInt(),
      'name': getEnv('APP_NAME'),
      'description': await cart.cartShortDesc(),
      'prefill': {
        "name": [
          billingDetails!.billingAddress?.firstName,
          billingDetails.billingAddress?.lastName
        ].where((t) => t != null || t != "").toList().join(" "),
        "method": "card",
        'email': billingDetails.billingAddress?.emailAddress ?? ""
      }
    };

    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": true});

    razorpay.open(options);
  });
}

void _handleExternalWallet(ExternalWalletResponse response) {}
