1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.woosignal.android"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-67
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-78
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-76
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-65
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-63
18    <!-- Samsung -->
19    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
19-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
19-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
20    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
20-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
20-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
21    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
21-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
21-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
22    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
22-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
22-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
23    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
23-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
23-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
24    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
25    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
26    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
27    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
28    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
29    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- See https://developer.android.com/training/package-visibility/declaring for more details. -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
30    <queries>
30-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
31
32        <!-- Added to check the default browser that will host the AuthFlow. -->
33        <intent>
33-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
34            <action android:name="android.intent.action.VIEW" />
34-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
34-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
35
36            <data android:scheme="http" />
36-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
36-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
37        </intent>
38        <intent>
38-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
39            <action android:name="android.support.customtabs.action.CustomTabsService" />
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
40        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
41        <package android:name="com.android.chrome" />
41-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
41-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
42
43        <intent>
43-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
44            <action android:name="android.intent.action.VIEW" />
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
45
46            <data
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
47                android:mimeType="*/*"
48                android:scheme="*" />
48-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
49        </intent>
50        <intent>
50-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
51            <action android:name="android.intent.action.VIEW" />
51-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
51-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
52
53            <category android:name="android.intent.category.BROWSABLE" />
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
54
55            <data
55-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
56                android:host="pay"
57                android:mimeType="*/*"
58                android:scheme="upi" />
58-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
59        </intent>
60        <intent>
60-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
61            <action android:name="android.intent.action.MAIN" />
61-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
61-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
62        </intent>
63        <intent>
63-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
64            <action android:name="android.intent.action.SEND" />
64-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
64-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
65
66            <data android:mimeType="*/*" />
66-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
67        </intent>
68        <intent>
68-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
69            <action android:name="rzp.device_token.share" />
69-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
69-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
70        </intent> <!-- Needs to be explicitly declared on Android R+ -->
71        <package android:name="com.google.android.apps.maps" />
71-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:9-64
71-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:33:18-61
72    </queries>
73
74    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
74-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
74-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
75    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
75-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
75-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
76    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
76-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
76-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
77
78    <uses-feature
78-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:26:5-28:35
79        android:glEsVersion="0x00020000"
79-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:27:9-41
80        android:required="true" />
80-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:28:9-32
81
82    <permission
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
83        android:name="com.woosignal.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
84        android:protectionLevel="signature" />
84-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
85
86    <uses-permission android:name="com.woosignal.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
86-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
86-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
87
88    <application
89        android:name="android.app.Application"
90        android:allowBackup="false"
91        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
91-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
92        android:debuggable="true"
93        android:extractNativeLibs="true"
94        android:fullBackupContent="false"
95        android:icon="@mipmap/ic_launcher"
96        android:label="Label StoreMax" >
97        <activity
98            android:name="com.woosignal.android.MainActivity"
99            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
100            android:exported="true"
101            android:hardwareAccelerated="true"
102            android:launchMode="singleTop"
103            android:screenOrientation="portrait"
104            android:theme="@style/LaunchTheme"
105            android:windowSoftInputMode="adjustResize" >
106
107            <!--
108                 Specifies an Android theme to apply to this Activity as soon as
109                 the Android process has started. This theme is visible to the user
110                 while the Flutter UI initializes. After that, this theme continues
111                 to determine the Window background behind the Flutter UI.
112            -->
113            <meta-data
114                android:name="io.flutter.embedding.android.NormalTheme"
115                android:resource="@style/NormalTheme" />
116
117            <intent-filter>
118                <action android:name="android.intent.action.MAIN" />
118-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
118-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
119
120                <category android:name="android.intent.category.LAUNCHER" />
121            </intent-filter>
122        </activity>
123        <!--
124             Don't delete the meta-data below.
125             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
126        -->
127        <meta-data
128            android:name="flutterEmbedding"
129            android:value="2" />
130
131        <activity
131-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
132            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
132-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
133            android:exported="true"
133-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
134            android:launchMode="singleTask" >
134-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
135            <intent-filter>
135-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
136                <action android:name="android.intent.action.VIEW" />
136-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
136-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
137
138                <category android:name="android.intent.category.DEFAULT" />
138-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
138-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
139                <category android:name="android.intent.category.BROWSABLE" />
139-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
139-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
140
141                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
142                <data
142-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
143                    android:host="link-accounts"
144                    android:pathPrefix="/com.woosignal.android/authentication_return"
145                    android:scheme="stripe-auth" />
145-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
146
147                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
148                <data
148-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
149                    android:host="link-native-accounts"
150                    android:pathPrefix="/com.woosignal.android/authentication_return"
151                    android:scheme="stripe-auth" />
151-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
152
153                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
154                <data
154-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
155                    android:host="link-accounts"
156                    android:path="/com.woosignal.android/success"
157                    android:scheme="stripe-auth" />
157-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
158                <data
158-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
159                    android:host="link-accounts"
160                    android:path="/com.woosignal.android/cancel"
161                    android:scheme="stripe-auth" />
161-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
162
163                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
164                <data
164-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
165                    android:host="native-redirect"
166                    android:pathPrefix="/com.woosignal.android"
167                    android:scheme="stripe-auth" />
167-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
168
169                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
170                <data
170-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
171                    android:host="auth-redirect"
172                    android:pathPrefix="/com.woosignal.android"
173                    android:scheme="stripe" />
173-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
174            </intent-filter>
175        </activity>
176        <activity
176-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
177            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
177-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
178            android:exported="false"
178-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
179            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
179-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
180        <activity
180-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
181            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
181-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
182            android:exported="false"
182-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
183            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
183-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
184            android:windowSoftInputMode="adjustResize" />
184-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
185        <activity
185-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
186            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
186-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
187            android:exported="false"
187-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
188            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
188-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
189
190        <service
190-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
191            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
191-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
192            android:exported="false"
192-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
193            android:permission="android.permission.BIND_JOB_SERVICE" />
193-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
194        <service
194-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
195            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
195-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
196            android:exported="false" >
196-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
197            <intent-filter>
197-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
198                <action android:name="com.google.firebase.MESSAGING_EVENT" />
198-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
198-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
199            </intent-filter>
200        </service>
201
202        <receiver
202-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
203            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
203-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
204            android:exported="true"
204-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
205            android:permission="com.google.android.c2dm.permission.SEND" >
205-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
206            <intent-filter>
206-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
207                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
207-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
207-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
208            </intent-filter>
209        </receiver>
210
211        <service
211-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
212            android:name="com.google.firebase.components.ComponentDiscoveryService"
212-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
213            android:directBootAware="true"
213-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
214            android:exported="false" >
214-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
215            <meta-data
215-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
216                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
216-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
218            <meta-data
218-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
219                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
219-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
221            <meta-data
221-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
222                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
222-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
224            <meta-data
224-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
225                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
225-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
227            <meta-data
227-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
228                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
228-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
230            <meta-data
230-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
231                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
231-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
233            <meta-data
233-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
234                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
234-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
235                android:value="com.google.firebase.components.ComponentRegistrar" />
235-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
236            <meta-data
236-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
237                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
237-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
238                android:value="com.google.firebase.components.ComponentRegistrar" />
238-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
239            <meta-data
239-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
240                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
240-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
241                android:value="com.google.firebase.components.ComponentRegistrar" />
241-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
242        </service>
243
244        <provider
244-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
245            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
245-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
246            android:authorities="com.woosignal.android.flutterfirebasemessaginginitprovider"
246-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
247            android:exported="false"
247-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
248            android:initOrder="99" />
248-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
249
250        <activity
250-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
251            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
251-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
252            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
252-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
253            android:exported="false"
253-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
254            android:theme="@style/AppTheme" />
254-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
255        <activity
255-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
256            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
256-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
257            android:exported="false"
257-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
258            android:theme="@style/ThemeTransparent" />
258-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
259        <activity
259-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
260            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
260-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
261            android:exported="false"
261-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
262            android:theme="@style/ThemeTransparent" />
262-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
263        <activity
263-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
264            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
264-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
265            android:exported="false"
265-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
266            android:launchMode="singleInstance"
266-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
267            android:theme="@style/ThemeTransparent" />
267-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
268        <activity
268-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
269            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
269-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
270            android:exported="false"
270-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
271            android:launchMode="singleInstance"
271-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
272            android:theme="@style/ThemeTransparent" />
272-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
273
274        <receiver
274-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
275            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
275-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
276            android:enabled="true"
276-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
277            android:exported="false" />
277-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
278
279        <meta-data
279-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
280            android:name="io.flutter.embedded_views_preview"
280-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
281            android:value="true" />
281-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
282
283        <activity
283-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
284            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
284-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
285            android:exported="false"
285-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
286            android:theme="@style/StripePaymentSheetDefaultTheme" />
286-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
287        <activity
287-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
288            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
288-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
289            android:exported="false"
289-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
290            android:theme="@style/StripePaymentSheetDefaultTheme" />
290-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
291        <activity
291-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
292            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
292-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
293            android:exported="false"
293-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
294            android:theme="@style/StripePaymentSheetDefaultTheme" />
294-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
295        <activity
295-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
296            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
296-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
297            android:exported="false"
297-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
298            android:theme="@style/StripePaymentSheetDefaultTheme" />
298-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
299        <activity
299-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
300            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
300-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
301            android:exported="false"
301-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
302            android:theme="@style/StripePaymentSheetDefaultTheme" />
302-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
303        <activity
303-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
304            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
304-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
305            android:exported="false"
305-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
306            android:theme="@style/StripePaymentSheetDefaultTheme" />
306-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
307        <activity
307-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
308            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
308-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
309            android:exported="false"
309-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
310            android:theme="@style/StripePaymentSheetDefaultTheme" />
310-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
311        <activity
311-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
312            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
312-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
313            android:exported="false"
313-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
314            android:theme="@style/StripePayLauncherDefaultTheme" />
314-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
315        <activity
315-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
316            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
316-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
317            android:theme="@style/StripePaymentSheetDefaultTheme" />
317-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
318        <activity
318-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
319            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
319-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
320            android:theme="@style/StripePaymentSheetDefaultTheme" />
320-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
321        <activity
321-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
322            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
322-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
323            android:theme="@style/StripePaymentSheetDefaultTheme" />
323-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
324        <activity
324-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
325            android:name="com.stripe.android.link.LinkActivity"
325-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
326            android:autoRemoveFromRecents="true"
326-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
327            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
327-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
328            android:exported="false"
328-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
329            android:label="@string/stripe_link"
329-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
330            android:theme="@style/StripeLinkBaseTheme"
330-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
331            android:windowSoftInputMode="adjustResize" />
331-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
332        <activity
332-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
333            android:name="com.stripe.android.link.LinkForegroundActivity"
333-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
334            android:autoRemoveFromRecents="true"
334-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
335            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
335-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
336            android:launchMode="singleTop"
336-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
337            android:theme="@style/StripeTransparentTheme" />
337-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
338        <activity
338-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
339            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
339-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
340            android:autoRemoveFromRecents="true"
340-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
341            android:exported="true"
341-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
342            android:launchMode="singleInstance"
342-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
343            android:theme="@style/StripeTransparentTheme" >
343-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
344            <intent-filter>
344-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
345                <action android:name="android.intent.action.VIEW" />
345-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
345-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
346
347                <category android:name="android.intent.category.DEFAULT" />
347-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
347-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
348                <category android:name="android.intent.category.BROWSABLE" />
348-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
348-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
349
350                <data
350-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
351                    android:host="complete"
352                    android:path="/com.woosignal.android"
353                    android:scheme="link-popup" />
353-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
354            </intent-filter>
355        </activity>
356        <activity
356-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
357            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
357-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
358            android:exported="false"
358-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
359            android:theme="@style/StripePaymentSheetDefaultTheme" />
359-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
360        <activity
360-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
361            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
361-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
362            android:exported="false"
362-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
363            android:theme="@style/StripeDefaultTheme" />
363-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
364        <activity
364-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
365            android:name="com.stripe.android.view.PaymentRelayActivity"
365-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
366            android:exported="false"
366-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
367            android:theme="@style/StripeTransparentTheme" />
367-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
368        <!--
369        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
370        launched the browser Activity will also handle the return URL deep link.
371        -->
372        <activity
372-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
373            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
373-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
374            android:exported="false"
374-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
375            android:launchMode="singleTask"
375-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
376            android:theme="@style/StripeTransparentTheme" />
376-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
377        <activity
377-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
378            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
378-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
379            android:exported="true"
379-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
380            android:launchMode="singleTask"
380-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
381            android:theme="@style/StripeTransparentTheme" >
381-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
382            <intent-filter>
382-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
383                <action android:name="android.intent.action.VIEW" />
383-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
383-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
384
385                <category android:name="android.intent.category.DEFAULT" />
385-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
385-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
386                <category android:name="android.intent.category.BROWSABLE" />
386-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
386-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
387
388                <!-- Must match `DefaultReturnUrl#value`. -->
389                <data
389-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
390                    android:host="payment_return_url"
391                    android:path="/com.woosignal.android"
392                    android:scheme="stripesdk" />
392-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
393            </intent-filter>
394        </activity>
395        <activity
395-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
396            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
396-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
397            android:exported="false"
397-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
398            android:theme="@style/StripeDefaultTheme" />
398-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
399        <activity
399-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
400            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
400-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
401            android:exported="false"
401-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
402            android:theme="@style/StripeGooglePayDefaultTheme" />
402-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
403        <activity
403-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
404            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
404-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
405            android:exported="false"
405-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
406            android:theme="@style/StripeGooglePayDefaultTheme" />
406-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
407        <activity
407-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
408            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
408-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
409            android:exported="false"
409-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
410            android:theme="@style/StripePayLauncherDefaultTheme" />
410-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
411        <activity
411-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
412            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
412-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
413            android:exported="false"
413-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
414            android:theme="@style/StripeTransparentTheme" />
414-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
415
416        <uses-library
416-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
417            android:name="androidx.window.extensions"
417-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
418            android:required="false" />
418-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
419        <uses-library
419-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
420            android:name="androidx.window.sidecar"
420-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
421            android:required="false" />
421-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
422
423        <activity
423-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
424            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
424-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
425            android:exported="false"
425-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
426            android:theme="@style/Stripe3DS2Theme" />
426-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
427
428        <receiver
428-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
429            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
429-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
430            android:exported="true"
430-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
431            android:permission="com.google.android.c2dm.permission.SEND" >
431-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
432            <intent-filter>
432-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
433                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
433-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
433-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
434            </intent-filter>
435
436            <meta-data
436-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
437                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
437-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
438                android:value="true" />
438-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
439        </receiver>
440        <!--
441             FirebaseMessagingService performs security checks at runtime,
442             but set to not exported to explicitly avoid allowing another app to call it.
443        -->
444        <service
444-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
445            android:name="com.google.firebase.messaging.FirebaseMessagingService"
445-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
446            android:directBootAware="true"
446-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
447            android:exported="false" >
447-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
448            <intent-filter android:priority="-500" >
448-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
449                <action android:name="com.google.firebase.MESSAGING_EVENT" />
449-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
449-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
450            </intent-filter>
451        </service>
452
453        <activity
453-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
454            android:name="com.razorpay.CheckoutActivity"
454-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
455            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
455-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
456            android:exported="false"
456-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
457            android:theme="@style/CheckoutTheme" >
457-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
458            <intent-filter>
458-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
459                <action android:name="android.intent.action.MAIN" />
459-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
459-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
460            </intent-filter>
461        </activity>
462
463        <provider
463-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
464            android:name="androidx.startup.InitializationProvider"
464-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
465            android:authorities="com.woosignal.android.androidx-startup"
465-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
466            android:exported="false" >
466-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
467            <meta-data
467-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
468                android:name="com.razorpay.RazorpayInitializer"
468-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
469                android:value="androidx.startup" />
469-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
470            <meta-data
470-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
471                android:name="androidx.emoji2.text.EmojiCompatInitializer"
471-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
472                android:value="androidx.startup" />
472-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
473            <meta-data
473-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
474                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
474-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
475                android:value="androidx.startup" />
475-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
476            <meta-data
476-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
477                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
477-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
478                android:value="androidx.startup" />
478-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
479        </provider>
480
481        <activity
481-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
482            android:name="com.razorpay.MagicXActivity"
482-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
483            android:exported="false"
483-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
484            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
484-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
485
486        <meta-data
486-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
487            android:name="com.razorpay.plugin.googlepay_all"
487-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
488            android:value="com.razorpay.RzpGpayMerged" />
488-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
489
490        <activity
490-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
491            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
491-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
492            android:excludeFromRecents="true"
492-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
493            android:exported="false"
493-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
494            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
494-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
495        <!--
496            Service handling Google Sign-In user revocation. For apps that do not integrate with
497            Google Sign-In, this service will never be started.
498        -->
499        <service
499-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
500            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
500-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
501            android:exported="true"
501-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
502            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
502-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
503            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
503-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
504        <uses-library
504-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:39:9-41:40
505            android:name="org.apache.http.legacy"
505-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:40:13-50
506            android:required="false" />
506-->[com.google.android.gms:play-services-maps:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\489854d1cbba2c461e09b70cb1905813\transformed\jetified-play-services-maps-18.0.2\AndroidManifest.xml:41:13-37
507
508        <activity
508-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
509            android:name="com.google.android.gms.common.api.GoogleApiActivity"
509-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
510            android:exported="false"
510-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
511            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
511-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
512
513        <provider
513-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
514            android:name="com.google.firebase.provider.FirebaseInitProvider"
514-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
515            android:authorities="com.woosignal.android.firebaseinitprovider"
515-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
516            android:directBootAware="true"
516-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
517            android:exported="false"
517-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
518            android:initOrder="100" />
518-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
519
520        <meta-data
520-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
521            android:name="com.google.android.gms.version"
521-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
522            android:value="@integer/google_play_services_version" />
522-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
523
524        <receiver
524-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
525            android:name="androidx.profileinstaller.ProfileInstallReceiver"
525-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
526            android:directBootAware="false"
526-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
527            android:enabled="true"
527-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
528            android:exported="true"
528-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
529            android:permission="android.permission.DUMP" >
529-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
530            <intent-filter>
530-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
531                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
532            </intent-filter>
533            <intent-filter>
533-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
534                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
535            </intent-filter>
536            <intent-filter>
536-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
537                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
538            </intent-filter>
539            <intent-filter>
539-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
540                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
540-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
540-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
541            </intent-filter>
542        </receiver>
543
544        <service
544-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
545            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
545-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
546            android:exported="false" >
546-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
547            <meta-data
547-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
548                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
548-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
549                android:value="cct" />
549-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
550        </service>
551        <service
551-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
552            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
552-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
553            android:exported="false"
553-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
554            android:permission="android.permission.BIND_JOB_SERVICE" >
554-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
555        </service>
556
557        <receiver
557-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
558            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
559            android:exported="false" />
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
560
561        <meta-data
561-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
562            android:name="aia-compat-api-min-version"
562-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
563            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
563-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
564        <activity
564-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
565            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
565-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
566            android:exported="false"
566-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
567            android:stateNotNeeded="true"
567-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
568            android:theme="@style/Theme.PlayCore.Transparent" />
568-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
569    </application>
570
571</manifest>
