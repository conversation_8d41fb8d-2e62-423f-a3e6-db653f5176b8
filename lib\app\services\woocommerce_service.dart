import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart';

/// WooCommerce API Service
/// Provides centralized access to WooCommerce REST API functionality
class WooCommerceService {
  static WooCommerceService? _instance;
  late WooCommerceFlutterApi _woocommerceClient;

  WooCommerceService._internal();

  static WooCommerceService get instance {
    _instance ??= WooCommerceService._internal();
    return _instance!;
  }

  /// Initialize the WooCommerce client with credentials from environment
  void initialize() {
    String baseUrl = getEnv('WOO_BASE_URL', defaultValue: '');
    String consumerKey = getEnv('WOO_CONSUMER_KEY', defaultValue: '');
    String consumerSecret = getEnv('WOO_CONSUMER_SECRET', defaultValue: '');

    if (baseUrl.isEmpty || consumerKey.isEmpty || consumerSecret.isEmpty) {
      throw Exception('WooCommerce credentials not found in .env file');
    }

    _woocommerceClient = WooCommerceFlutterApi(
      baseUrl: baseUrl,
      consumerKey: consumerKey,
      consumerSecret: consumerSecret,
      isDebug: getEnv('APP_DEBUG', defaultValue: 'false') == 'true',
    );
  }

  /// Get the WooCommerce client instance
  WooCommerceFlutterApi get client {
    return _woocommerceClient;
  }

  /// Products API methods
  Future<List<WooProduct>> getProducts({
    int page = 1,
    int perPage = 10,
    String? search,
    List<int>? categories,
    String orderBy = 'date',
    String order = 'desc',
    String? status,
    bool? featured,
    String? sku,
    String? slug,
  }) async {
    try {
      return await _woocommerceClient.getProducts(
        page: page,
        perPage: perPage,
        search: search,
        category: categories?.join(','),
        orderBy: orderBy,
        order: order,
        status: status,
        featured: featured,
        sku: sku,
        slug: slug,
      );
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  Future<WooProduct> getProduct(int productId) async {
    try {
      return await _woocommerceClient.getProductById(productId);
    } catch (e) {
      throw Exception('Failed to fetch product: $e');
    }
  }

  Future<List<WooProductVariation>> getProductVariations(int productId) async {
    try {
      return await _woocommerceClient.getProductVariations(productId);
    } catch (e) {
      throw Exception('Failed to fetch product variations: $e');
    }
  }

  /// Categories API methods
  Future<List<WooProductCategory>> getCategories({
    int page = 1,
    int perPage = 100,
    String orderBy = 'name',
    String order = 'asc',
    bool hideEmpty = true,
    int? parent,
  }) async {
    try {
      return await _woocommerceClient.getProductCategories(
        page: page,
        perPage: perPage,
        orderBy: orderBy,
        order: order,
        hideEmpty: hideEmpty,
        parent: parent,
      );
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  /// Orders API methods
  Future<WooOrder> createOrder(WooOrderPayload orderPayload) async {
    try {
      return await _woocommerceClient.createOrder(orderPayload);
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  Future<List<WooOrder>> getOrders({
    int page = 1,
    int perPage = 10,
    String? status,
    int? customer,
    String? orderBy,
    String? order,
  }) async {
    try {
      return await _woocommerceClient.getOrders(
        page: page,
        perPage: perPage,
        status: status,
        customer: customer,
        orderBy: orderBy,
        order: order,
      );
    } catch (e) {
      throw Exception('Failed to fetch orders: $e');
    }
  }

  /// Customers API methods
  Future<WooCustomer> createCustomer(WooCustomer customer) async {
    try {
      return await _woocommerceClient.createCustomer(customer);
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }

  Future<WooCustomer> getCustomer(int customerId) async {
    try {
      return await _woocommerceClient.getCustomerById(customerId);
    } catch (e) {
      throw Exception('Failed to fetch customer: $e');
    }
  }

  /// Coupons API methods
  Future<List<WooCoupon>> getCoupons({
    int page = 1,
    int perPage = 10,
    String? search,
    String? code,
  }) async {
    try {
      return await _woocommerceClient.getCoupons(
        page: page,
        perPage: perPage,
        search: search,
        code: code,
      );
    } catch (e) {
      throw Exception('Failed to fetch coupons: $e');
    }
  }

  /// Shipping methods
  Future<List<WooShippingZone>> getShippingZones() async {
    try {
      return await _woocommerceClient.getShippingZones();
    } catch (e) {
      throw Exception('Failed to fetch shipping zones: $e');
    }
  }

  Future<List<WooShippingMethod>> getShippingMethods(int zoneId) async {
    try {
      return await _woocommerceClient.getShippingZoneMethods(zoneId);
    } catch (e) {
      throw Exception('Failed to fetch shipping methods: $e');
    }
  }

  /// Tax rates
  Future<List<WooTaxRate>> getTaxRates({
    String? taxClass,
  }) async {
    try {
      return await _woocommerceClient.getTaxRates(taxClass: taxClass);
    } catch (e) {
      throw Exception('Failed to fetch tax rates: $e');
    }
  }

  /// Product reviews
  Future<List<WooProductReview>> getProductReviews(int productId, {
    int page = 1,
    int perPage = 10,
  }) async {
    try {
      return await _woocommerceClient.getProductReviews(
        productId: productId,
        page: page,
        perPage: perPage,
      );
    } catch (e) {
      throw Exception('Failed to fetch product reviews: $e');
    }
  }

  Future<WooProductReview> createProductReview(WooProductReview review) async {
    try {
      return await _woocommerceClient.createProductReview(review);
    } catch (e) {
      throw Exception('Failed to create product review: $e');
    }
  }
}
