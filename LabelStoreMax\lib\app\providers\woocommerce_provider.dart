//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';

class WooCommerceProvider extends NyProvider {
  @override
  boot(Nylo nylo) async {
    // Initialize WooCommerce service
    WooCommerceService.instance.initialize();
    return nylo;
  }
  
  @override
  afterBoot(Nylo nylo) async {
    // Any post-initialization logic can go here
  }
}
