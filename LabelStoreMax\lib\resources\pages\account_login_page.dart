//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/forms/login_form.dart';
import '/resources/widgets/buttons/buttons.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/app/events/login_event.dart';
import '/resources/pages/account_register_page.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class AccountLoginPage extends NyStatefulWidget {
  static RouteView path = ("/account-login", (_) => AccountLoginPage());
  final bool showBackButton;
  AccountLoginPage({super.key, this.showBackButton = true})
      : super(child: () => _AccountLoginPageState());
}

class _AccountLoginPageState extends NyPage<AccountLoginPage> {
  LoginForm form = LoginForm();

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  StoreLogo(height: 100),
                  Flexible(
                    child: Container(
                      height: 70,
                      padding: EdgeInsets.only(bottom: 20),
                      margin: EdgeInsets.symmetric(horizontal: 20),
                      alignment: Alignment.bottomLeft,
                      child: Text(
                        trans("Login"),
                        textAlign: TextAlign.left,
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium!
                            .copyWith(
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                            ),
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      boxShadow:
                          (Theme.of(context).brightness == Brightness.light)
                              ? wsBoxShadow()
                              : null,
                      color: ThemeColor.get(context).backgroundContainer,
                    ),
                    padding: EdgeInsets.only(
                        top: 20, bottom: 15, left: 16, right: 16),
                    margin: EdgeInsets.symmetric(horizontal: 16),
                    child: NyForm(
                        form: form,
                        crossAxisSpacing: 15,
                        footer: Padding(
                            padding: EdgeInsets.only(top: 20),
                            child: Button.primary(
                              text: trans("Login"),
                              submitForm: (
                                form,
                                (data) async {
                                  await _loginUser(
                                      data['email'], data['password']);
                                }
                              ),
                            ))),
                  ),
                ],
              ),
            ),
            TextButton(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Icon(
                    Icons.account_circle,
                    color: (Theme.of(context).brightness == Brightness.light)
                        ? Colors.black38
                        : Colors.white70,
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Text(
                      trans("Create an account"),
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  )
                ],
              ),
              onPressed: () => routeTo(AccountRegistrationPage.path),
            ),
            LinkButton(
                title: trans("Forgot Password"),
                action: () {
                  String? forgotPasswordUrl =
                      AppHelper.instance.appConfig!.wpLoginForgotPasswordUrl;
                  if (forgotPasswordUrl != null) {
                    openBrowserTab(url: forgotPasswordUrl);
                  } else {
                    NyLogger.info(
                        "No URL found for \"forgot password\".\nAdd your forgot password URL here https://woosignal.com/dashboard/apps");
                  }
                }),
            widget.showBackButton
                ? Column(
                    children: [
                      Divider(),
                      LinkButton(
                        title: trans("Back"),
                        action: () => Navigator.pop(context),
                      ),
                    ],
                  )
                : Padding(
                    padding: EdgeInsets.only(bottom: 20),
                  )
          ],
        ),
      ),
    );
  }

  _loginUser(String email, String password) async {
    if (email.isNotEmpty) {
      email = email.trim();
    }

    await lockRelease('login_button', perform: () async {
      bool loginSuccess = false;
      try {
        // TODO: Implement proper WooCommerce authentication
        // For now, we'll use a placeholder authentication system
        // This should be replaced with JWT authentication or WordPress REST API

        // Validate email format
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
          showToast(
              title: trans("Invalid details"),
              description: trans("Please enter a valid email address"),
              style: ToastNotificationStyleType.danger);
          return;
        }

        // Validate password length
        if (password.length < 6) {
          showToast(
              title: trans("Invalid details"),
              description: trans("Password must be at least 6 characters"),
              style: ToastNotificationStyleType.danger);
          return;
        }

        // Try to find customer by email to validate existence
        // This is a basic validation - in production, use proper authentication
        try {
          // For now, we'll assume login is successful if email format is valid
          // TODO: Replace with actual authentication API call
          await Future.delayed(Duration(milliseconds: 500)); // Simulate API call

          // Store user session (placeholder)
          await NyStorage.save('user_email', email);
          await NyStorage.save('user_logged_in', true);

          loginSuccess = true;
        } catch (e) {
          showToast(
              title: trans("Invalid details"),
              description: trans("Invalid login credentials"),
              style: ToastNotificationStyleType.danger);
          return;
        }
      } on Exception catch (e) {
        showToast(
            title: trans("Oops!"),
            description: trans("Something went wrong, please try again"),
            style: ToastNotificationStyleType.danger,
            icon: Icons.account_circle);
        NyLogger.error("Login error: $e");
        return;
      }

      if (!loginSuccess) {
        return;
      }

      event<LoginEvent>();

      showToast(
          title: trans("Hello"),
          description: trans("Welcome back"),
          style: ToastNotificationStyleType.success,
          icon: Icons.account_circle);
      if (!mounted) return;
      navigatorPush(context,
          routeName: UserAuth.instance.redirect, forgetLast: 1);
    });
  }
}
