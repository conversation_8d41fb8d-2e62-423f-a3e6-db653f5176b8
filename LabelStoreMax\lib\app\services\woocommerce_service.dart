//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart';

class WooCommerceService {
  static final WooCommerceService _instance = WooCommerceService._internal();
  late WooCommerce _wooCommerce;
  
  factory WooCommerceService() {
    return _instance;
  }
  
  WooCommerceService._internal() {
    _wooCommerce = WooCommerce(
      baseUrl: getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly'),
      username: getEnv('WOO_CONSUMER_KEY', defaultValue: 'ck_18128d84570a5c30870239c83e9cfcae0be0b4f9'),
      password: getEnv('WOO_CONSUMER_SECRET', defaultValue: 'cs_e079d293dbffc0b44387b7eba6d8704f83638cb2'),
      useFaker: false,
      isDebug: getEnv('APP_DEBUG', defaultValue: 'true') == 'true',
    );
  }
  
  WooCommerce get wooCommerce => _wooCommerce;
  
  // Convenience methods for common operations
  Future<List<WooProduct>> getProducts({
    int page = 1,
    int perPage = 10,
    String? search,
    int? category,
    List<int>? include,
    WooSortOrderBy orderBy = WooSortOrderBy.date,
    WooSortOrder order = WooSortOrder.desc,
    WooFilterStatus status = WooFilterStatus.publish,
    WooProductStockStatus? stockStatus,
  }) async {
    return await _wooCommerce.getProducts(
      page: page,
      perPage: perPage,
      search: search,
      category: category,
      include: include,
      orderBy: orderBy,
      order: order,
      status: status,
      stockStatus: stockStatus,
    );
  }
  
  Future<WooProduct> getProduct(int id) async {
    return await _wooCommerce.getProduct(id);
  }
  
  Future<List<WooProductCategory>> getProductCategories({
    int page = 1,
    int perPage = 10,
    int? parent,
    bool? hideEmpty,
    List<int>? include,
    WooSortOrder order = WooSortOrder.asc,
    WooCategoryOrderBy orderBy = WooCategoryOrderBy.name,
  }) async {
    return await _wooCommerce.getCategories(
      page: page,
      perPage: perPage,
      parent: parent,
      hideEmpty: hideEmpty,
      include: include,
      order: order,
      orderBy: orderBy,
    );
  }
  
  Future<List<WooProductVariation>> getProductVariations(int productId, {
    int page = 1,
    int perPage = 10,
  }) async {
    return await _wooCommerce.getProductVaritaions(
      productId,
      page: page,
      perPage: perPage,
    );
  }
  
  Future<WooOrder> createOrder(WooOrder order) async {
    return await _wooCommerce.createOrder(order);
  }
  
  Future<List<WooOrder>> getOrders({
    int page = 1,
    int perPage = 10,
    int? customer,
    List<WooOrderStatus> status = const [WooOrderStatus.any],
  }) async {
    return await _wooCommerce.getOrders(
      page: page,
      perPage: perPage,
      customer: customer,
      status: status,
    );
  }

  Future<WooOrder> getOrder(int id) async {
    return await _wooCommerce.getOrder(id);
  }
}
