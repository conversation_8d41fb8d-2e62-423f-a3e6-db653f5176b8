//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/pages/product_detail_page.dart';
import '/bootstrap/enums/sort_enums.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/woosignal_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/services/woocommerce_service.dart';

class BrowseCategoryPage extends NyStatefulWidget {
  static RouteView path = ("/browse-category", (_) => BrowseCategoryPage());

  BrowseCategoryPage({super.key})
      : super(child: () => _BrowseCategoryPageState());
}

class _BrowseCategoryPageState extends NyPage<BrowseCategoryPage> {
  WooProductCategory? productCategory;
  SortByType? _sortByType;

  @override
  get init => () {
        productCategory = widget.controller.data();
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(trans("Browse"),
                style: Theme.of(context).textTheme.titleMedium),
            afterNotNull(productCategory,
                child: () => Text(parseHtmlString(productCategory!.name)),
                loading: CupertinoActivityIndicator())
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.tune),
            onPressed: _modalSheetTune,
          )
        ],
      ),
      body: SafeAreaWidget(
          child: NyPullToRefresh.grid(
        data: (page) async {
          List<WooProduct> products = await WooCommerceService().getProducts(
            page: page,
            perPage: 25,
            category: productCategory?.id,
            status: WooFilterStatus.publish,
            stockStatus: WooProductStockStatus.instock,
          );
          return products;
        },
        child: (context, product) {
          return SizedBox(
            height: 300,
            child: ProductItemContainer(
              product: product,
              onTap: () => _showProduct(product),
            ),
          );
        },
        stateName: 'browse_category_pull_to_refresh',
        sort: (products) {
          return _sortProducts(products,
              by: _sortByType ?? SortByType.dateDesc);
        },
      )),
    );
  }

  _sortProducts(List<WooProduct> products, {required SortByType by}) {
    switch (by) {
      case SortByType.lowToHigh:
        products.sort(
          (product1, product2) => (double.tryParse(product1.price?.toString() ?? "0") ?? 0.0)
              .compareTo((double.tryParse(product2.price?.toString() ?? "0") ?? 0.0)),
        );
        break;
      case SortByType.highToLow:
        products.sort(
          (product1, product2) => (double.tryParse(product2.price?.toString() ?? "0") ?? 0.0)
              .compareTo((double.tryParse(product1.price?.toString() ?? "0") ?? 0.0)),
        );
        break;
      case SortByType.nameAZ:
        products.sort(
          (product1, product2) => product1.name!.compareTo(product2.name!),
        );
        break;
      case SortByType.nameZA:
        products.sort(
          (product1, product2) => product2.name!.compareTo(product1.name!),
        );
        break;
      case SortByType.dateAsc:
        products.sort((product1, product2) {
          DateTime? date1 = product1.dateCreated;
          DateTime? date2 = product2.dateCreated;
          if (date1 == null || date2 == null) return 0;
          return date1.compareTo(date2);
        });
        break;
      case SortByType.dateDesc:
        products.sort((product1, product2) {
          DateTime? date1 = product1.dateCreated;
          DateTime? date2 = product2.dateCreated;
          if (date1 == null || date2 == null) return 0;
          return date2.compareTo(date1);
        });
        break;
    }
    return products;
  }

  _modalSheetTune() {
    wsModalBottom(
      context,
      title: trans("Sort results"),
      bodyWidget: ListView(
        children: [
          LinkButton(
            title: trans("Sort: Low to high"),
            selected: _sortByType == SortByType.lowToHigh,
            action: () {
              _sortByType = SortByType.lowToHigh;
              StateAction.refreshPage('browse_category_pull_to_refresh',
                  setState: () {});
              pop();
            },
          ),
          Divider(
            height: 0,
          ),
          LinkButton(
            title: trans("Sort: High to low"),
            selected: _sortByType == SortByType.highToLow,
            action: () {
              _sortByType = SortByType.highToLow;
              StateAction.refreshPage('browse_category_pull_to_refresh',
                  setState: () {});
              pop();
            },
          ),
          Divider(
            height: 0,
          ),
          LinkButton(
            title: trans("Sort: Name A-Z"),
            selected: _sortByType == SortByType.nameAZ,
            action: () {
              _sortByType = SortByType.nameAZ;
              StateAction.refreshPage('browse_category_pull_to_refresh',
                  setState: () {});
              pop();
            },
          ),
          Divider(
            height: 0,
          ),
          LinkButton(
            title: trans("Sort: Name Z-A"),
            selected: _sortByType == SortByType.nameZA,
            action: () {
              _sortByType = SortByType.nameZA;
              StateAction.refreshPage('browse_category_pull_to_refresh',
                  setState: () {});
              pop();
            },
          ),
          Divider(
            height: 0,
          ),
          LinkButton(
            title: trans("Sort: Date New to Old"),
            selected: _sortByType == SortByType.dateDesc,
            action: () {
              _sortByType = SortByType.dateDesc;
              StateAction.refreshPage('browse_category_pull_to_refresh',
                  setState: () {});
              pop();
            },
          ),
          Divider(
            height: 0,
          ),
          LinkButton(
            title: trans("Sort: Date Old to New"),
            selected: _sortByType == SortByType.dateAsc,
            action: () {
              _sortByType = SortByType.dateAsc;
              StateAction.refreshPage('browse_category_pull_to_refresh',
                  setState: () {});
              pop();
            },
          ),
          Divider(
            height: 0,
          ),
          LinkButton(title: trans("Cancel"), action: pop)
        ],
      ),
    );
  }

  _showProduct(WooProduct product) {
    routeTo(ProductDetailPage.path, data: product);
  }
}
