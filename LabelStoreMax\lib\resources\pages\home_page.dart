//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import '/app/events/category_notification_event.dart';
import '/resources/pages/account_order_detail_page.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/events/firebase_on_message_order_event.dart';
import '/app/events/order_notification_event.dart';
import '/app/events/product_notification_event.dart';
import '/bootstrap/helpers.dart';
import '/bootstrap/app_helper.dart';
import '/resources/widgets/compo_theme_widget.dart';
import '/resources/widgets/mello_theme_widget.dart';
import '/resources/widgets/notic_theme_widget.dart';
import '/app/models/app_config.dart';

class HomePage extends NyStatefulWidget {
  static RouteView path = ("/home", (_) => HomePage());

  HomePage({super.key}) : super(child: () => _HomePageState());
}

class _HomePageState extends NyPage<HomePage> {
  final AppConfig? _appConfig = AppHelper.instance.appConfig;

  @override
  get init => () async {
        await _enableFcmNotifications();
        await sleep(4);
      };

  _enableFcmNotifications() async {
    bool? firebaseFcmIsEnabled =
        AppHelper.instance.appConfig?.firebaseFcmIsEnabled;
    firebaseFcmIsEnabled ??= getEnv('FCM_ENABLED', defaultValue: false);

    if (firebaseFcmIsEnabled != true) return;

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      /// WP Notify - Product notification
      if (message.data.containsKey('product_id')) {
        event<ProductNotificationEvent>(data: {"RemoteMessage": message});
      }

      /// WP Notify - Order notification
      if (message.data.containsKey('order_id')) {
        event<OrderNotificationEvent>(data: {"RemoteMessage": message});
      }

      /// WooSignal - Category notification
      if (message.data.containsKey('category_id')) {
        event<CategoryNotificationEvent>(data: {"RemoteMessage": message});
      }
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      /// WP Notify - Order notification
      if (message.data.containsKey('order_id')) {
        event<FirebaseOnMessageOrderEvent>(data: {"RemoteMessage": message});
        _maybeShowSnackBar(message);
      }
    });
  }

  /// Attempt to show a snackbar if the user is on the same page
  _maybeShowSnackBar(RemoteMessage message) async {
    if (!(await canSeeRemoteMessage(message))) {
      return;
    }
    _showSnackBar(message.notification?.body, onPressed: () {
      routeTo(AccountOrderDetailPage.path,
          data: int.parse(message.data['order_id']));
    });
  }

  _showSnackBar(String? message, {Function()? onPressed}) {
    SnackBar snackBar = SnackBar(
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${'New notification received'.tr()} 🚨',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          if (message != null) Text(message)
        ],
      ),
      action: onPressed == null
          ? null
          : SnackBarAction(
              label: 'View'.tr(),
              onPressed: onPressed,
            ),
      duration: Duration(milliseconds: 4500),
    );

    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  @override
  Widget view(BuildContext context) {
    return match(
        AppHelper.instance.appConfig?.theme,
        () => {
              "notic": NoticThemeWidget(wooSignalApp: _appConfig),
              "compo": CompoThemeWidget(wooSignalApp: _appConfig),
              "mello": MelloThemeWidget(wooSignalApp: _appConfig),
            },
        defaultValue: MelloThemeWidget(wooSignalApp: _appConfig));
  }
}
