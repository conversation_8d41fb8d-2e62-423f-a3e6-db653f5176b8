//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

class AppConfig {
  String? theme;
  String? themeFont;
  Map<String, dynamic>? themeColors;
  Map<String, dynamic>? currencyMeta;
  bool? firebaseFcmIsEnabled;
  int? wpLoginEnabled;
  String? wpLoginBaseUrl;
  String? wpLoginForgotPasswordUrl;
  String? wpLoginWpApiPath;
  String? locale;
  String? paypalLocale;
  String? stripeAccount;
  String? stripeCountryCode;
  bool? stripeLiveMode;
  String? razorpayKeyId;
  bool? razorpayLiveMode;
  String? paypalEmail;
  bool? paypalLiveMode;
  bool? showRelatedProducts;
  bool? showUpsellProducts;
  bool? wishlistEnabled;
  bool? showProductReviews;
  bool? disableShipping;
  String? bannerHeight;
  String? productPricesIncludeTax;
  String? wpLoginUserRole;
  String? appStatus;
  
  AppConfig({
    this.theme,
    this.themeFont,
    this.themeColors,
    this.currencyMeta,
    this.firebaseFcmIsEnabled,
    this.wpLoginEnabled,
    this.wpLoginBaseUrl,
    this.wpLoginForgotPasswordUrl,
    this.wpLoginWpApiPath,
    this.locale,
    this.paypalLocale,
    this.stripeAccount,
    this.stripeCountryCode,
    this.stripeLiveMode,
    this.razorpayKeyId,
    this.razorpayLiveMode,
    this.paypalEmail,
    this.paypalLiveMode,
    this.showRelatedProducts,
    this.showUpsellProducts,
    this.wishlistEnabled,
    this.showProductReviews,
    this.disableShipping,
    this.bannerHeight,
    this.productPricesIncludeTax,
    this.wpLoginUserRole,
    this.appStatus,
  });
  
  // Factory constructor to create a default configuration
  factory AppConfig.defaultConfig() {
    return AppConfig(
      theme: "mello",
      themeFont: "Poppins",
      themeColors: {
        'light': {
          'background': '0xFFFFFFFF',
          'primary_text': '0xFF000000',
          'button_background': '0xFF529cda',
          'button_text': '0xFFFFFFFF',
          'app_bar_background': '0xFFFFFFFF',
          'app_bar_text': '0xFF3a3d40',
        },
        'dark': {
          'background': '0xFF212121',
          'primary_text': '0xFFE1E1E1',
          'button_background': '0xFFFFFFFF',
          'button_text': '0xFF232c33',
          'app_bar_background': '0xFF2C2C2C',
          'app_bar_text': '0xFFFFFFFF',
        }
      },
      currencyMeta: {
        'symbol': 'LYD',
        'symbolNative': 'د.ل',
        'decimalDigits': 2,
        'rounding': 0,
        'code': 'LYD',
        'namePlural': 'Libyan dinars'
      },
      firebaseFcmIsEnabled: false,
      wpLoginEnabled: 0,
      showRelatedProducts: true,
      showUpsellProducts: true,
      wishlistEnabled: true,
      showProductReviews: true,
      disableShipping: false,
      productPricesIncludeTax: "no",
      appStatus: "active",
    );
  }
}
